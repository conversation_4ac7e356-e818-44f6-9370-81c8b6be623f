#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Système de Gestion de Location
Application principale avec interface Tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime, date
import os

class DatabaseManager:
    """Gestionnaire de base de données SQLite"""
    
    def __init__(self, db_name="location_system.db"):
        self.db_name = db_name
        self.init_database()
    
    def init_database(self):
        """Initialise la base de données avec les tables nécessaires"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Table des clients
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prenom TEXT NOT NULL,
                telephone TEXT,
                email TEXT,
                adresse TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des biens
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS biens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type_bien TEXT NOT NULL,
                adresse TEXT NOT NULL,
                superficie REAL,
                nb_pieces INTEGER,
                loyer_mensuel REAL NOT NULL,
                charges REAL DEFAULT 0,
                disponible BOOLEAN DEFAULT 1,
                description TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des contrats
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS contrats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_id INTEGER,
                bien_id INTEGER,
                date_debut DATE NOT NULL,
                date_fin DATE,
                loyer_mensuel REAL NOT NULL,
                depot_garantie REAL,
                statut TEXT DEFAULT 'actif',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients (id),
                FOREIGN KEY (bien_id) REFERENCES biens (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def execute_query(self, query, params=None):
        """Exécute une requête SQL"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            result = cursor.fetchall()
        else:
            conn.commit()
            result = cursor.lastrowid
        
        conn.close()
        return result

class LocationApp:
    """Application principale de gestion de location"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Système de Gestion de Location")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # Initialiser la base de données
        self.db = DatabaseManager()
        
        # Créer l'interface
        self.create_widgets()
        
    def create_widgets(self):
        """Crée l'interface utilisateur"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Titre
        title_label = ttk.Label(main_frame, text="Système de Gestion de Location", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Menu de navigation
        self.create_navigation_menu(main_frame)
        
        # Zone de contenu principal
        self.content_frame = ttk.Frame(main_frame)
        self.content_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(20, 0))
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)
        
        # Afficher la page d'accueil par défaut
        self.show_dashboard()
    
    def create_navigation_menu(self, parent):
        """Crée le menu de navigation"""
        nav_frame = ttk.LabelFrame(parent, text="Navigation", padding="10")
        nav_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 20))
        
        # Boutons de navigation
        buttons = [
            ("🏠 Tableau de bord", self.show_dashboard),
            ("👥 Gestion Clients", self.show_clients),
            ("🏢 Gestion Biens", self.show_biens),
            ("📋 Gestion Contrats", self.show_contrats),
            ("📊 Rapports", self.show_reports)
        ]
        
        for i, (text, command) in enumerate(buttons):
            btn = ttk.Button(nav_frame, text=text, command=command, width=20)
            btn.grid(row=i, column=0, pady=5, sticky=(tk.W, tk.E))
    
    def clear_content(self):
        """Efface le contenu de la zone principale"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """Affiche le tableau de bord"""
        self.clear_content()

        dashboard_frame = ttk.LabelFrame(self.content_frame, text="Tableau de bord", padding="20")
        dashboard_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        dashboard_frame.columnconfigure(0, weight=1)

        # Statistiques
        stats_frame = ttk.Frame(dashboard_frame)
        stats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # Compter les éléments
        nb_clients = len(self.db.execute_query("SELECT * FROM clients"))
        nb_biens = len(self.db.execute_query("SELECT * FROM biens"))
        nb_contrats_actifs = len(self.db.execute_query("SELECT * FROM contrats WHERE statut = 'actif'"))

        stats = [
            ("Nombre de clients", nb_clients),
            ("Nombre de biens", nb_biens),
            ("Contrats actifs", nb_contrats_actifs)
        ]

        for i, (label, value) in enumerate(stats):
            stat_frame = ttk.LabelFrame(stats_frame, text=label, padding="10")
            stat_frame.grid(row=0, column=i, padx=10, sticky=(tk.W, tk.E))

            value_label = ttk.Label(stat_frame, text=str(value), font=('Arial', 24, 'bold'))
            value_label.grid(row=0, column=0)

        # Actions rapides
        actions_frame = ttk.LabelFrame(dashboard_frame, text="Actions rapides", padding="20")
        actions_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(20, 0))

        quick_actions = [
            ("Ajouter un client", self.add_client),
            ("Ajouter un bien", self.add_bien),
            ("Créer un contrat", self.add_contrat)
        ]

        for i, (text, command) in enumerate(quick_actions):
            btn = ttk.Button(actions_frame, text=text, command=command)
            btn.grid(row=0, column=i, padx=10)

    def show_clients(self):
        """Affiche la gestion des clients"""
        self.clear_content()

        clients_frame = ttk.LabelFrame(self.content_frame, text="Gestion des Clients", padding="10")
        clients_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        clients_frame.columnconfigure(0, weight=1)
        clients_frame.rowconfigure(1, weight=1)

        # Boutons d'action
        btn_frame = ttk.Frame(clients_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="Ajouter Client", command=self.add_client).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Modifier Client", command=self.edit_client).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(btn_frame, text="Supprimer Client", command=self.delete_client).grid(row=0, column=2)

        # Liste des clients
        self.clients_tree = ttk.Treeview(clients_frame, columns=('ID', 'Nom', 'Prénom', 'Téléphone', 'Email'), show='headings')
        self.clients_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration des colonnes
        self.clients_tree.heading('ID', text='ID')
        self.clients_tree.heading('Nom', text='Nom')
        self.clients_tree.heading('Prénom', text='Prénom')
        self.clients_tree.heading('Téléphone', text='Téléphone')
        self.clients_tree.heading('Email', text='Email')

        self.clients_tree.column('ID', width=50)
        self.clients_tree.column('Nom', width=150)
        self.clients_tree.column('Prénom', width=150)
        self.clients_tree.column('Téléphone', width=120)
        self.clients_tree.column('Email', width=200)

        # Scrollbar
        scrollbar_clients = ttk.Scrollbar(clients_frame, orient=tk.VERTICAL, command=self.clients_tree.yview)
        scrollbar_clients.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.clients_tree.configure(yscrollcommand=scrollbar_clients.set)

        # Charger les données
        self.load_clients()

    def load_clients(self):
        """Charge la liste des clients"""
        # Effacer les données existantes
        for item in self.clients_tree.get_children():
            self.clients_tree.delete(item)

        # Charger les clients depuis la base de données
        clients = self.db.execute_query("SELECT id, nom, prenom, telephone, email FROM clients ORDER BY nom, prenom")

        for client in clients:
            self.clients_tree.insert('', 'end', values=client)

    def add_client(self):
        """Ouvre la fenêtre d'ajout de client"""
        self.client_form_window(mode='add')

    def edit_client(self):
        """Ouvre la fenêtre de modification de client"""
        selected = self.clients_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un client à modifier.")
            return

        client_id = self.clients_tree.item(selected[0])['values'][0]
        self.client_form_window(mode='edit', client_id=client_id)

    def delete_client(self):
        """Supprime un client"""
        selected = self.clients_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un client à supprimer.")
            return

        client_id = self.clients_tree.item(selected[0])['values'][0]
        client_nom = self.clients_tree.item(selected[0])['values'][1]
        client_prenom = self.clients_tree.item(selected[0])['values'][2]

        if messagebox.askyesno("Confirmation", f"Êtes-vous sûr de vouloir supprimer le client {client_prenom} {client_nom} ?"):
            self.db.execute_query("DELETE FROM clients WHERE id = ?", (client_id,))
            self.load_clients()
            messagebox.showinfo("Succès", "Client supprimé avec succès.")

    def client_form_window(self, mode='add', client_id=None):
        """Fenêtre de formulaire pour ajouter/modifier un client"""
        window = tk.Toplevel(self.root)
        window.title("Ajouter Client" if mode == 'add' else "Modifier Client")
        window.geometry("400x300")
        window.resizable(False, False)

        # Centrer la fenêtre
        window.transient(self.root)
        window.grab_set()

        # Frame principal
        main_frame = ttk.Frame(window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Variables pour les champs
        nom_var = tk.StringVar()
        prenom_var = tk.StringVar()
        telephone_var = tk.StringVar()
        email_var = tk.StringVar()
        adresse_var = tk.StringVar()

        # Si mode modification, charger les données existantes
        if mode == 'edit' and client_id:
            client_data = self.db.execute_query("SELECT nom, prenom, telephone, email, adresse FROM clients WHERE id = ?", (client_id,))
            if client_data:
                nom_var.set(client_data[0][0] or '')
                prenom_var.set(client_data[0][1] or '')
                telephone_var.set(client_data[0][2] or '')
                email_var.set(client_data[0][3] or '')
                adresse_var.set(client_data[0][4] or '')

        # Champs du formulaire
        ttk.Label(main_frame, text="Nom *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        nom_entry = ttk.Entry(main_frame, textvariable=nom_var, width=30)
        nom_entry.grid(row=0, column=1, pady=5, padx=(10, 0))

        ttk.Label(main_frame, text="Prénom *:").grid(row=1, column=0, sticky=tk.W, pady=5)
        prenom_entry = ttk.Entry(main_frame, textvariable=prenom_var, width=30)
        prenom_entry.grid(row=1, column=1, pady=5, padx=(10, 0))

        ttk.Label(main_frame, text="Téléphone:").grid(row=2, column=0, sticky=tk.W, pady=5)
        telephone_entry = ttk.Entry(main_frame, textvariable=telephone_var, width=30)
        telephone_entry.grid(row=2, column=1, pady=5, padx=(10, 0))

        ttk.Label(main_frame, text="Email:").grid(row=3, column=0, sticky=tk.W, pady=5)
        email_entry = ttk.Entry(main_frame, textvariable=email_var, width=30)
        email_entry.grid(row=3, column=1, pady=5, padx=(10, 0))

        ttk.Label(main_frame, text="Adresse:").grid(row=4, column=0, sticky=tk.W, pady=5)
        adresse_text = tk.Text(main_frame, width=30, height=3)
        adresse_text.grid(row=4, column=1, pady=5, padx=(10, 0))
        adresse_text.insert('1.0', adresse_var.get())

        # Boutons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=5, column=0, columnspan=2, pady=20)

        def save_client():
            # Validation
            if not nom_var.get().strip() or not prenom_var.get().strip():
                messagebox.showerror("Erreur", "Le nom et le prénom sont obligatoires.")
                return

            # Récupérer les données
            nom = nom_var.get().strip()
            prenom = prenom_var.get().strip()
            telephone = telephone_var.get().strip()
            email = email_var.get().strip()
            adresse = adresse_text.get('1.0', tk.END).strip()

            try:
                if mode == 'add':
                    self.db.execute_query(
                        "INSERT INTO clients (nom, prenom, telephone, email, adresse) VALUES (?, ?, ?, ?, ?)",
                        (nom, prenom, telephone, email, adresse)
                    )
                    messagebox.showinfo("Succès", "Client ajouté avec succès.")
                else:
                    self.db.execute_query(
                        "UPDATE clients SET nom=?, prenom=?, telephone=?, email=?, adresse=? WHERE id=?",
                        (nom, prenom, telephone, email, adresse, client_id)
                    )
                    messagebox.showinfo("Succès", "Client modifié avec succès.")

                self.load_clients()
                window.destroy()

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        ttk.Button(btn_frame, text="Enregistrer", command=save_client).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Annuler", command=window.destroy).grid(row=0, column=1)

        # Focus sur le premier champ
        nom_entry.focus()

    def show_biens(self):
        """Affiche la gestion des biens"""
        self.clear_content()

        biens_frame = ttk.LabelFrame(self.content_frame, text="Gestion des Biens", padding="10")
        biens_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        biens_frame.columnconfigure(0, weight=1)
        biens_frame.rowconfigure(1, weight=1)

        # Boutons d'action
        btn_frame = ttk.Frame(biens_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="Ajouter Bien", command=self.add_bien).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Modifier Bien", command=self.edit_bien).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(btn_frame, text="Supprimer Bien", command=self.delete_bien).grid(row=0, column=2)

        # Liste des biens
        self.biens_tree = ttk.Treeview(biens_frame, columns=('ID', 'Type', 'Adresse', 'Superficie', 'Pièces', 'Loyer', 'Disponible'), show='headings')
        self.biens_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration des colonnes
        self.biens_tree.heading('ID', text='ID')
        self.biens_tree.heading('Type', text='Type')
        self.biens_tree.heading('Adresse', text='Adresse')
        self.biens_tree.heading('Superficie', text='Superficie (m²)')
        self.biens_tree.heading('Pièces', text='Pièces')
        self.biens_tree.heading('Loyer', text='Loyer (€)')
        self.biens_tree.heading('Disponible', text='Disponible')

        self.biens_tree.column('ID', width=50)
        self.biens_tree.column('Type', width=100)
        self.biens_tree.column('Adresse', width=200)
        self.biens_tree.column('Superficie', width=100)
        self.biens_tree.column('Pièces', width=80)
        self.biens_tree.column('Loyer', width=100)
        self.biens_tree.column('Disponible', width=80)

        # Scrollbar
        scrollbar_biens = ttk.Scrollbar(biens_frame, orient=tk.VERTICAL, command=self.biens_tree.yview)
        scrollbar_biens.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.biens_tree.configure(yscrollcommand=scrollbar_biens.set)

        # Charger les données
        self.load_biens()

    def load_biens(self):
        """Charge la liste des biens"""
        # Effacer les données existantes
        for item in self.biens_tree.get_children():
            self.biens_tree.delete(item)

        # Charger les biens depuis la base de données
        biens = self.db.execute_query("""
            SELECT id, type_bien, adresse, superficie, nb_pieces, loyer_mensuel, disponible
            FROM biens ORDER BY adresse
        """)

        for bien in biens:
            disponible_text = "Oui" if bien[6] else "Non"
            values = list(bien[:-1]) + [disponible_text]
            self.biens_tree.insert('', 'end', values=values)

    def add_bien(self):
        """Ouvre la fenêtre d'ajout de bien"""
        self.bien_form_window(mode='add')

    def edit_bien(self):
        """Ouvre la fenêtre de modification de bien"""
        selected = self.biens_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un bien à modifier.")
            return

        bien_id = self.biens_tree.item(selected[0])['values'][0]
        self.bien_form_window(mode='edit', bien_id=bien_id)

    def delete_bien(self):
        """Supprime un bien"""
        selected = self.biens_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un bien à supprimer.")
            return

        bien_id = self.biens_tree.item(selected[0])['values'][0]
        bien_adresse = self.biens_tree.item(selected[0])['values'][2]

        if messagebox.askyesno("Confirmation", f"Êtes-vous sûr de vouloir supprimer le bien à l'adresse {bien_adresse} ?"):
            self.db.execute_query("DELETE FROM biens WHERE id = ?", (bien_id,))
            self.load_biens()
            messagebox.showinfo("Succès", "Bien supprimé avec succès.")

    def bien_form_window(self, mode='add', bien_id=None):
        """Fenêtre de formulaire pour ajouter/modifier un bien"""
        window = tk.Toplevel(self.root)
        window.title("Ajouter Bien" if mode == 'add' else "Modifier Bien")
        window.geometry("450x400")
        window.resizable(False, False)

        # Centrer la fenêtre
        window.transient(self.root)
        window.grab_set()

        # Frame principal
        main_frame = ttk.Frame(window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Variables pour les champs
        type_var = tk.StringVar()
        adresse_var = tk.StringVar()
        superficie_var = tk.StringVar()
        pieces_var = tk.StringVar()
        loyer_var = tk.StringVar()
        charges_var = tk.StringVar()
        disponible_var = tk.BooleanVar(value=True)
        description_var = tk.StringVar()

        # Si mode modification, charger les données existantes
        if mode == 'edit' and bien_id:
            bien_data = self.db.execute_query("""
                SELECT type_bien, adresse, superficie, nb_pieces, loyer_mensuel, charges, disponible, description
                FROM biens WHERE id = ?
            """, (bien_id,))
            if bien_data:
                type_var.set(bien_data[0][0] or '')
                adresse_var.set(bien_data[0][1] or '')
                superficie_var.set(str(bien_data[0][2]) if bien_data[0][2] else '')
                pieces_var.set(str(bien_data[0][3]) if bien_data[0][3] else '')
                loyer_var.set(str(bien_data[0][4]) if bien_data[0][4] else '')
                charges_var.set(str(bien_data[0][5]) if bien_data[0][5] else '')
                disponible_var.set(bool(bien_data[0][6]))
                description_var.set(bien_data[0][7] or '')

        # Champs du formulaire
        row = 0

        ttk.Label(main_frame, text="Type de bien *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        type_combo = ttk.Combobox(main_frame, textvariable=type_var, width=27)
        type_combo['values'] = ('Appartement', 'Maison', 'Studio', 'Loft', 'Bureau', 'Local commercial')
        type_combo.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Adresse *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        adresse_entry = ttk.Entry(main_frame, textvariable=adresse_var, width=30)
        adresse_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Superficie (m²):").grid(row=row, column=0, sticky=tk.W, pady=5)
        superficie_entry = ttk.Entry(main_frame, textvariable=superficie_var, width=30)
        superficie_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Nombre de pièces:").grid(row=row, column=0, sticky=tk.W, pady=5)
        pieces_entry = ttk.Entry(main_frame, textvariable=pieces_var, width=30)
        pieces_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Loyer mensuel (€) *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        loyer_entry = ttk.Entry(main_frame, textvariable=loyer_var, width=30)
        loyer_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Charges (€):").grid(row=row, column=0, sticky=tk.W, pady=5)
        charges_entry = ttk.Entry(main_frame, textvariable=charges_var, width=30)
        charges_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Checkbutton(main_frame, text="Disponible", variable=disponible_var).grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        row += 1

        ttk.Label(main_frame, text="Description:").grid(row=row, column=0, sticky=tk.W, pady=5)
        description_text = tk.Text(main_frame, width=30, height=3)
        description_text.grid(row=row, column=1, pady=5, padx=(10, 0))
        description_text.insert('1.0', description_var.get())
        row += 1

        # Boutons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=row, column=0, columnspan=2, pady=20)

        def save_bien():
            # Validation
            if not type_var.get().strip() or not adresse_var.get().strip() or not loyer_var.get().strip():
                messagebox.showerror("Erreur", "Le type, l'adresse et le loyer sont obligatoires.")
                return

            try:
                # Validation des nombres
                superficie = float(superficie_var.get()) if superficie_var.get().strip() else None
                pieces = int(pieces_var.get()) if pieces_var.get().strip() else None
                loyer = float(loyer_var.get())
                charges = float(charges_var.get()) if charges_var.get().strip() else 0

                # Récupérer les données
                type_bien = type_var.get().strip()
                adresse = adresse_var.get().strip()
                disponible = disponible_var.get()
                description = description_text.get('1.0', tk.END).strip()

                if mode == 'add':
                    self.db.execute_query("""
                        INSERT INTO biens (type_bien, adresse, superficie, nb_pieces, loyer_mensuel, charges, disponible, description)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (type_bien, adresse, superficie, pieces, loyer, charges, disponible, description))
                    messagebox.showinfo("Succès", "Bien ajouté avec succès.")
                else:
                    self.db.execute_query("""
                        UPDATE biens SET type_bien=?, adresse=?, superficie=?, nb_pieces=?, loyer_mensuel=?,
                        charges=?, disponible=?, description=? WHERE id=?
                    """, (type_bien, adresse, superficie, pieces, loyer, charges, disponible, description, bien_id))
                    messagebox.showinfo("Succès", "Bien modifié avec succès.")

                self.load_biens()
                window.destroy()

            except ValueError:
                messagebox.showerror("Erreur", "Veuillez entrer des valeurs numériques valides pour la superficie, les pièces et le loyer.")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        ttk.Button(btn_frame, text="Enregistrer", command=save_bien).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Annuler", command=window.destroy).grid(row=0, column=1)

        # Focus sur le premier champ
        type_combo.focus()

    def show_contrats(self):
        """Affiche la gestion des contrats"""
        self.clear_content()

        contrats_frame = ttk.LabelFrame(self.content_frame, text="Gestion des Contrats", padding="10")
        contrats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        contrats_frame.columnconfigure(0, weight=1)
        contrats_frame.rowconfigure(1, weight=1)

        # Boutons d'action
        btn_frame = ttk.Frame(contrats_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="Nouveau Contrat", command=self.add_contrat).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Modifier Contrat", command=self.edit_contrat).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(btn_frame, text="Résilier Contrat", command=self.terminate_contrat).grid(row=0, column=2)

        # Liste des contrats
        self.contrats_tree = ttk.Treeview(contrats_frame, columns=('ID', 'Client', 'Bien', 'Début', 'Fin', 'Loyer', 'Statut'), show='headings')
        self.contrats_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration des colonnes
        self.contrats_tree.heading('ID', text='ID')
        self.contrats_tree.heading('Client', text='Client')
        self.contrats_tree.heading('Bien', text='Bien')
        self.contrats_tree.heading('Début', text='Date début')
        self.contrats_tree.heading('Fin', text='Date fin')
        self.contrats_tree.heading('Loyer', text='Loyer (€)')
        self.contrats_tree.heading('Statut', text='Statut')

        self.contrats_tree.column('ID', width=50)
        self.contrats_tree.column('Client', width=150)
        self.contrats_tree.column('Bien', width=200)
        self.contrats_tree.column('Début', width=100)
        self.contrats_tree.column('Fin', width=100)
        self.contrats_tree.column('Loyer', width=100)
        self.contrats_tree.column('Statut', width=80)

        # Scrollbar
        scrollbar_contrats = ttk.Scrollbar(contrats_frame, orient=tk.VERTICAL, command=self.contrats_tree.yview)
        scrollbar_contrats.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.contrats_tree.configure(yscrollcommand=scrollbar_contrats.set)

        # Charger les données
        self.load_contrats()

    def load_contrats(self):
        """Charge la liste des contrats"""
        # Effacer les données existantes
        for item in self.contrats_tree.get_children():
            self.contrats_tree.delete(item)

        # Charger les contrats depuis la base de données avec jointures
        contrats = self.db.execute_query("""
            SELECT c.id, cl.prenom || ' ' || cl.nom as client, b.adresse,
                   c.date_debut, c.date_fin, c.loyer_mensuel, c.statut
            FROM contrats c
            JOIN clients cl ON c.client_id = cl.id
            JOIN biens b ON c.bien_id = b.id
            ORDER BY c.date_creation DESC
        """)

        for contrat in contrats:
            self.contrats_tree.insert('', 'end', values=contrat)

    def add_contrat(self):
        """Ouvre la fenêtre d'ajout de contrat"""
        self.contrat_form_window(mode='add')

    def edit_contrat(self):
        """Ouvre la fenêtre de modification de contrat"""
        selected = self.contrats_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un contrat à modifier.")
            return

        contrat_id = self.contrats_tree.item(selected[0])['values'][0]
        self.contrat_form_window(mode='edit', contrat_id=contrat_id)

    def terminate_contrat(self):
        """Résilie un contrat"""
        selected = self.contrats_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un contrat à résilier.")
            return

        contrat_id = self.contrats_tree.item(selected[0])['values'][0]
        client_nom = self.contrats_tree.item(selected[0])['values'][1]

        if messagebox.askyesno("Confirmation", f"Êtes-vous sûr de vouloir résilier le contrat de {client_nom} ?"):
            self.db.execute_query("UPDATE contrats SET statut = 'résilié', date_fin = ? WHERE id = ?",
                                (date.today().isoformat(), contrat_id))
            # Rendre le bien disponible
            bien_id = self.db.execute_query("SELECT bien_id FROM contrats WHERE id = ?", (contrat_id,))[0][0]
            self.db.execute_query("UPDATE biens SET disponible = 1 WHERE id = ?", (bien_id,))

            self.load_contrats()
            messagebox.showinfo("Succès", "Contrat résilié avec succès.")

    def contrat_form_window(self, mode='add', contrat_id=None):
        """Fenêtre de formulaire pour ajouter/modifier un contrat"""
        window = tk.Toplevel(self.root)
        window.title("Nouveau Contrat" if mode == 'add' else "Modifier Contrat")
        window.geometry("450x350")
        window.resizable(False, False)

        # Centrer la fenêtre
        window.transient(self.root)
        window.grab_set()

        # Frame principal
        main_frame = ttk.Frame(window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Variables pour les champs
        client_var = tk.StringVar()
        bien_var = tk.StringVar()
        date_debut_var = tk.StringVar(value=date.today().isoformat())
        date_fin_var = tk.StringVar()
        loyer_var = tk.StringVar()
        depot_var = tk.StringVar()

        # Récupérer les listes de clients et biens
        clients = self.db.execute_query("SELECT id, prenom || ' ' || nom FROM clients ORDER BY nom, prenom")
        biens_disponibles = self.db.execute_query("SELECT id, type_bien || ' - ' || adresse FROM biens WHERE disponible = 1")

        # Si mode modification, charger les données existantes
        if mode == 'edit' and contrat_id:
            contrat_data = self.db.execute_query("""
                SELECT client_id, bien_id, date_debut, date_fin, loyer_mensuel, depot_garantie
                FROM contrats WHERE id = ?
            """, (contrat_id,))
            if contrat_data:
                # Trouver le client et le bien correspondants
                for client_id, client_nom in clients:
                    if client_id == contrat_data[0][0]:
                        client_var.set(f"{client_id} - {client_nom}")
                        break

                # Pour la modification, inclure aussi le bien actuellement loué
                tous_biens = self.db.execute_query("SELECT id, type_bien || ' - ' || adresse FROM biens")
                for bien_id, bien_nom in tous_biens:
                    if bien_id == contrat_data[0][1]:
                        bien_var.set(f"{bien_id} - {bien_nom}")
                        break

                date_debut_var.set(contrat_data[0][2] or '')
                date_fin_var.set(contrat_data[0][3] or '')
                loyer_var.set(str(contrat_data[0][4]) if contrat_data[0][4] else '')
                depot_var.set(str(contrat_data[0][5]) if contrat_data[0][5] else '')

        # Champs du formulaire
        row = 0

        ttk.Label(main_frame, text="Client *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        client_combo = ttk.Combobox(main_frame, textvariable=client_var, width=35)
        client_combo['values'] = [f"{c[0]} - {c[1]}" for c in clients]
        client_combo.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Bien *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        bien_combo = ttk.Combobox(main_frame, textvariable=bien_var, width=35)
        if mode == 'add':
            bien_combo['values'] = [f"{b[0]} - {b[1]}" for b in biens_disponibles]
        else:
            # En mode modification, montrer tous les biens
            tous_biens = self.db.execute_query("SELECT id, type_bien || ' - ' || adresse FROM biens")
            bien_combo['values'] = [f"{b[0]} - {b[1]}" for b in tous_biens]
        bien_combo.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Date début *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        date_debut_entry = ttk.Entry(main_frame, textvariable=date_debut_var, width=37)
        date_debut_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Date fin:").grid(row=row, column=0, sticky=tk.W, pady=5)
        date_fin_entry = ttk.Entry(main_frame, textvariable=date_fin_var, width=37)
        date_fin_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Loyer mensuel (€) *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        loyer_entry = ttk.Entry(main_frame, textvariable=loyer_var, width=37)
        loyer_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Dépôt de garantie (€):").grid(row=row, column=0, sticky=tk.W, pady=5)
        depot_entry = ttk.Entry(main_frame, textvariable=depot_var, width=37)
        depot_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        # Boutons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=row, column=0, columnspan=2, pady=20)

        def save_contrat():
            # Validation
            if not client_var.get().strip() or not bien_var.get().strip() or not date_debut_var.get().strip() or not loyer_var.get().strip():
                messagebox.showerror("Erreur", "Le client, le bien, la date de début et le loyer sont obligatoires.")
                return

            try:
                # Extraire les IDs
                client_id = int(client_var.get().split(' - ')[0])
                bien_id = int(bien_var.get().split(' - ')[0])

                # Validation des données
                date_debut = date_debut_var.get().strip()
                date_fin = date_fin_var.get().strip() if date_fin_var.get().strip() else None
                loyer = float(loyer_var.get())
                depot = float(depot_var.get()) if depot_var.get().strip() else None

                if mode == 'add':
                    self.db.execute_query("""
                        INSERT INTO contrats (client_id, bien_id, date_debut, date_fin, loyer_mensuel, depot_garantie)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (client_id, bien_id, date_debut, date_fin, loyer, depot))

                    # Marquer le bien comme non disponible
                    self.db.execute_query("UPDATE biens SET disponible = 0 WHERE id = ?", (bien_id,))

                    messagebox.showinfo("Succès", "Contrat créé avec succès.")
                else:
                    self.db.execute_query("""
                        UPDATE contrats SET client_id=?, bien_id=?, date_debut=?, date_fin=?,
                        loyer_mensuel=?, depot_garantie=? WHERE id=?
                    """, (client_id, bien_id, date_debut, date_fin, loyer, depot, contrat_id))
                    messagebox.showinfo("Succès", "Contrat modifié avec succès.")

                self.load_contrats()
                window.destroy()

            except ValueError:
                messagebox.showerror("Erreur", "Veuillez entrer des valeurs valides.")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        ttk.Button(btn_frame, text="Enregistrer", command=save_contrat).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Annuler", command=window.destroy).grid(row=0, column=1)

        # Focus sur le premier champ
        client_combo.focus()

    def show_reports(self):
        """Affiche les rapports"""
        self.clear_content()

        reports_frame = ttk.LabelFrame(self.content_frame, text="Rapports et Statistiques", padding="20")
        reports_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        reports_frame.columnconfigure(0, weight=1)

        # Statistiques détaillées
        stats_frame = ttk.LabelFrame(reports_frame, text="Statistiques générales", padding="15")
        stats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # Calculer les statistiques
        total_clients = len(self.db.execute_query("SELECT * FROM clients"))
        total_biens = len(self.db.execute_query("SELECT * FROM biens"))
        biens_loues = len(self.db.execute_query("SELECT * FROM biens WHERE disponible = 0"))
        biens_disponibles = total_biens - biens_loues
        contrats_actifs = len(self.db.execute_query("SELECT * FROM contrats WHERE statut = 'actif'"))

        # Revenus mensuels
        revenus_result = self.db.execute_query("""
            SELECT SUM(loyer_mensuel) FROM contrats
            WHERE statut = 'actif'
        """)
        revenus_mensuels = revenus_result[0][0] if revenus_result[0][0] else 0

        stats_text = f"""
Nombre total de clients: {total_clients}
Nombre total de biens: {total_biens}
Biens loués: {biens_loues}
Biens disponibles: {biens_disponibles}
Contrats actifs: {contrats_actifs}
Revenus mensuels totaux: {revenus_mensuels:.2f} €
Taux d'occupation: {(biens_loues/total_biens*100) if total_biens > 0 else 0:.1f}%
        """

        stats_label = ttk.Label(stats_frame, text=stats_text, font=('Arial', 10))
        stats_label.grid(row=0, column=0, sticky=tk.W)

        # Biens par type
        types_frame = ttk.LabelFrame(reports_frame, text="Répartition par type de bien", padding="15")
        types_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        types_data = self.db.execute_query("""
            SELECT type_bien, COUNT(*) as nombre,
                   SUM(CASE WHEN disponible = 0 THEN 1 ELSE 0 END) as loues
            FROM biens
            GROUP BY type_bien
        """)

        if types_data:
            types_text = "Type de bien | Total | Loués | Disponibles\n"
            types_text += "-" * 45 + "\n"
            for type_bien, total, loues in types_data:
                disponibles = total - loues
                types_text += f"{type_bien:<15} | {total:>3} | {loues:>3} | {disponibles:>3}\n"
        else:
            types_text = "Aucune donnée disponible"

        types_label = ttk.Label(types_frame, text=types_text, font=('Courier', 9))
        types_label.grid(row=0, column=0, sticky=tk.W)

        # Boutons d'export (fonctionnalité future)
        export_frame = ttk.LabelFrame(reports_frame, text="Export", padding="15")
        export_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))

        ttk.Button(export_frame, text="Exporter les clients (CSV)",
                  command=lambda: messagebox.showinfo("Info", "Fonctionnalité à venir")).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(export_frame, text="Exporter les contrats (CSV)",
                  command=lambda: messagebox.showinfo("Info", "Fonctionnalité à venir")).grid(row=0, column=1)

if __name__ == "__main__":
    root = tk.Tk()
    app = LocationApp(root)
    root.mainloop()
