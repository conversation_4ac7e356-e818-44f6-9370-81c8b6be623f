#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Système de Gestion de Location
Application principale avec interface Tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime, date
import os

class DatabaseManager:
    """Gestionnaire de base de données SQLite"""
    
    def __init__(self, db_name="location_system.db"):
        self.db_name = db_name
        self.init_database()
    
    def init_database(self):
        """Initialise la base de données avec les tables nécessaires"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Table des clients
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prenom TEXT NOT NULL,
                telephone TEXT,
                email TEXT,
                adresse TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des biens
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS biens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type_bien TEXT NOT NULL,
                adresse TEXT NOT NULL,
                superficie REAL,
                nb_pieces INTEGER,
                loyer_mensuel REAL NOT NULL,
                charges REAL DEFAULT 0,
                disponible BOOLEAN DEFAULT 1,
                description TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des voitures
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS voitures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                marque TEXT NOT NULL,
                modele TEXT NOT NULL,
                annee INTEGER,
                immatriculation TEXT UNIQUE NOT NULL,
                couleur TEXT,
                carburant TEXT,
                transmission TEXT,
                kilometrage INTEGER DEFAULT 0,
                prix_jour REAL NOT NULL,
                disponible BOOLEAN DEFAULT 1,
                description TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Table des contrats (modifiée pour supporter biens et voitures)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS contrats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_id INTEGER,
                bien_id INTEGER,
                voiture_id INTEGER,
                type_location TEXT NOT NULL CHECK (type_location IN ('bien', 'voiture')),
                date_debut DATE NOT NULL,
                date_fin DATE,
                prix_unitaire REAL NOT NULL,
                depot_garantie REAL,
                statut TEXT DEFAULT 'actif',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients (id),
                FOREIGN KEY (bien_id) REFERENCES biens (id),
                FOREIGN KEY (voiture_id) REFERENCES voitures (id)
            )
        ''')

        conn.commit()
        conn.close()
    
    def execute_query(self, query, params=None):
        """Exécute une requête SQL"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            result = cursor.fetchall()
        else:
            conn.commit()
            result = cursor.lastrowid
        
        conn.close()
        return result

class LocationApp:
    """Application principale de gestion de location"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Système de Gestion de Location")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # Initialiser la base de données
        self.db = DatabaseManager()
        
        # Créer l'interface
        self.create_widgets()
        
    def create_widgets(self):
        """Crée l'interface utilisateur"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Titre
        title_label = ttk.Label(main_frame, text="Système de Gestion de Location", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Menu de navigation
        self.create_navigation_menu(main_frame)
        
        # Zone de contenu principal avec scrollbar
        content_container = ttk.Frame(main_frame)
        content_container.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(20, 0))
        content_container.columnconfigure(0, weight=1)
        content_container.rowconfigure(0, weight=1)

        # Canvas pour le scroll
        self.content_canvas = tk.Canvas(content_container, highlightthickness=0)
        self.content_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbar verticale pour le contenu
        content_scrollbar = ttk.Scrollbar(content_container, orient=tk.VERTICAL, command=self.content_canvas.yview)
        content_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.content_canvas.configure(yscrollcommand=content_scrollbar.set)

        # Frame de contenu dans le canvas
        self.content_frame = ttk.Frame(self.content_canvas)
        self.content_window = self.content_canvas.create_window((0, 0), window=self.content_frame, anchor="nw")

        # Configurer le scroll
        def configure_scroll_region(event=None):
            self.content_canvas.configure(scrollregion=self.content_canvas.bbox("all"))

        def configure_canvas_width(event):
            canvas_width = event.width
            self.content_canvas.itemconfig(self.content_window, width=canvas_width)

        self.content_frame.bind('<Configure>', configure_scroll_region)
        self.content_canvas.bind('<Configure>', configure_canvas_width)

        # Scroll avec la molette de la souris
        def on_mousewheel(event):
            self.content_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        self.content_canvas.bind("<MouseWheel>", on_mousewheel)
        
        # Afficher la page d'accueil par défaut
        self.show_dashboard()
    
    def create_navigation_menu(self, parent):
        """Crée le menu de navigation"""
        nav_frame = ttk.LabelFrame(parent, text="Navigation", padding="10")
        nav_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 20))
        
        # Boutons de navigation
        buttons = [
            ("🏠 Tableau de bord", self.show_dashboard),
            ("👥 Gestion Clients", self.show_clients),
            ("🏢 Gestion Biens", self.show_biens),
            ("🚗 Gestion Voitures", self.show_voitures),
            ("📋 Gestion Contrats", self.show_contrats),
            ("📊 Rapports", self.show_reports)
        ]
        
        for i, (text, command) in enumerate(buttons):
            btn = ttk.Button(nav_frame, text=text, command=command, width=20)
            btn.grid(row=i, column=0, pady=5, sticky=(tk.W, tk.E))
    
    def clear_content(self):
        """Efface le contenu de la zone principale"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """Affiche le tableau de bord"""
        self.clear_content()

        dashboard_frame = ttk.LabelFrame(self.content_frame, text="Tableau de bord", padding="20")
        dashboard_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        dashboard_frame.columnconfigure(0, weight=1)

        # Statistiques
        stats_frame = ttk.Frame(dashboard_frame)
        stats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # Compter les éléments
        nb_clients = len(self.db.execute_query("SELECT * FROM clients"))
        nb_biens = len(self.db.execute_query("SELECT * FROM biens"))
        nb_voitures = len(self.db.execute_query("SELECT * FROM voitures"))
        nb_contrats_actifs = len(self.db.execute_query("SELECT * FROM contrats WHERE statut = 'actif'"))

        stats = [
            ("Nombre de clients", nb_clients),
            ("Nombre de biens", nb_biens),
            ("Nombre de voitures", nb_voitures),
            ("Contrats actifs", nb_contrats_actifs)
        ]

        for i, (label, value) in enumerate(stats):
            stat_frame = ttk.LabelFrame(stats_frame, text=label, padding="10")
            stat_frame.grid(row=0, column=i, padx=10, sticky=(tk.W, tk.E))

            value_label = ttk.Label(stat_frame, text=str(value), font=('Arial', 24, 'bold'))
            value_label.grid(row=0, column=0)

        # Actions rapides
        actions_frame = ttk.LabelFrame(dashboard_frame, text="Actions rapides", padding="20")
        actions_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(20, 0))

        quick_actions = [
            ("Ajouter un client", self.add_client),
            ("Ajouter un bien", self.add_bien),
            ("Ajouter une voiture", self.add_voiture),
            ("Créer un contrat", self.add_contrat)
        ]

        for i, (text, command) in enumerate(quick_actions):
            btn = ttk.Button(actions_frame, text=text, command=command)
            btn.grid(row=0, column=i, padx=10)

    def show_clients(self):
        """Affiche la gestion des clients"""
        self.clear_content()

        clients_frame = ttk.LabelFrame(self.content_frame, text="Gestion des Clients", padding="10")
        clients_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        clients_frame.columnconfigure(0, weight=1)
        clients_frame.rowconfigure(1, weight=1)

        # Boutons d'action
        btn_frame = ttk.Frame(clients_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="Ajouter Client", command=self.add_client).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Modifier Client", command=self.edit_client).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(btn_frame, text="Supprimer Client", command=self.delete_client).grid(row=0, column=2)

        # Liste des clients
        self.clients_tree = ttk.Treeview(clients_frame, columns=('ID', 'Nom', 'Prénom', 'Téléphone', 'Email'), show='headings')
        self.clients_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration des colonnes
        self.clients_tree.heading('ID', text='ID')
        self.clients_tree.heading('Nom', text='Nom')
        self.clients_tree.heading('Prénom', text='Prénom')
        self.clients_tree.heading('Téléphone', text='Téléphone')
        self.clients_tree.heading('Email', text='Email')

        self.clients_tree.column('ID', width=50)
        self.clients_tree.column('Nom', width=150)
        self.clients_tree.column('Prénom', width=150)
        self.clients_tree.column('Téléphone', width=120)
        self.clients_tree.column('Email', width=200)

        # Scrollbars
        scrollbar_clients_v = ttk.Scrollbar(clients_frame, orient=tk.VERTICAL, command=self.clients_tree.yview)
        scrollbar_clients_v.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.clients_tree.configure(yscrollcommand=scrollbar_clients_v.set)

        scrollbar_clients_h = ttk.Scrollbar(clients_frame, orient=tk.HORIZONTAL, command=self.clients_tree.xview)
        scrollbar_clients_h.grid(row=2, column=0, sticky=(tk.W, tk.E))
        self.clients_tree.configure(xscrollcommand=scrollbar_clients_h.set)

        # Charger les données
        self.load_clients()

    def load_clients(self):
        """Charge la liste des clients"""
        # Effacer les données existantes
        for item in self.clients_tree.get_children():
            self.clients_tree.delete(item)

        # Charger les clients depuis la base de données
        clients = self.db.execute_query("SELECT id, nom, prenom, telephone, email FROM clients ORDER BY nom, prenom")

        for client in clients:
            self.clients_tree.insert('', 'end', values=client)

    def add_client(self):
        """Ouvre la fenêtre d'ajout de client"""
        self.client_form_window(mode='add')

    def edit_client(self):
        """Ouvre la fenêtre de modification de client"""
        selected = self.clients_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un client à modifier.")
            return

        client_id = self.clients_tree.item(selected[0])['values'][0]
        self.client_form_window(mode='edit', client_id=client_id)

    def delete_client(self):
        """Supprime un client"""
        selected = self.clients_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un client à supprimer.")
            return

        client_id = self.clients_tree.item(selected[0])['values'][0]
        client_nom = self.clients_tree.item(selected[0])['values'][1]
        client_prenom = self.clients_tree.item(selected[0])['values'][2]

        if messagebox.askyesno("Confirmation", f"Êtes-vous sûr de vouloir supprimer le client {client_prenom} {client_nom} ?"):
            self.db.execute_query("DELETE FROM clients WHERE id = ?", (client_id,))
            self.load_clients()
            messagebox.showinfo("Succès", "Client supprimé avec succès.")

    def client_form_window(self, mode='add', client_id=None):
        """Fenêtre de formulaire pour ajouter/modifier un client"""
        window = tk.Toplevel(self.root)
        window.title("Ajouter Client" if mode == 'add' else "Modifier Client")
        window.geometry("400x300")
        window.resizable(False, False)

        # Centrer la fenêtre
        window.transient(self.root)
        window.grab_set()

        # Frame principal
        main_frame = ttk.Frame(window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Variables pour les champs
        nom_var = tk.StringVar()
        prenom_var = tk.StringVar()
        telephone_var = tk.StringVar()
        email_var = tk.StringVar()
        adresse_var = tk.StringVar()

        # Si mode modification, charger les données existantes
        if mode == 'edit' and client_id:
            client_data = self.db.execute_query("SELECT nom, prenom, telephone, email, adresse FROM clients WHERE id = ?", (client_id,))
            if client_data:
                nom_var.set(client_data[0][0] or '')
                prenom_var.set(client_data[0][1] or '')
                telephone_var.set(client_data[0][2] or '')
                email_var.set(client_data[0][3] or '')
                adresse_var.set(client_data[0][4] or '')

        # Champs du formulaire
        ttk.Label(main_frame, text="Nom *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        nom_entry = ttk.Entry(main_frame, textvariable=nom_var, width=30)
        nom_entry.grid(row=0, column=1, pady=5, padx=(10, 0))

        ttk.Label(main_frame, text="Prénom *:").grid(row=1, column=0, sticky=tk.W, pady=5)
        prenom_entry = ttk.Entry(main_frame, textvariable=prenom_var, width=30)
        prenom_entry.grid(row=1, column=1, pady=5, padx=(10, 0))

        ttk.Label(main_frame, text="Téléphone:").grid(row=2, column=0, sticky=tk.W, pady=5)
        telephone_entry = ttk.Entry(main_frame, textvariable=telephone_var, width=30)
        telephone_entry.grid(row=2, column=1, pady=5, padx=(10, 0))

        ttk.Label(main_frame, text="Email:").grid(row=3, column=0, sticky=tk.W, pady=5)
        email_entry = ttk.Entry(main_frame, textvariable=email_var, width=30)
        email_entry.grid(row=3, column=1, pady=5, padx=(10, 0))

        ttk.Label(main_frame, text="Adresse:").grid(row=4, column=0, sticky=tk.W, pady=5)

        # Frame pour le texte avec scrollbar
        adresse_frame = ttk.Frame(main_frame)
        adresse_frame.grid(row=4, column=1, pady=5, padx=(10, 0))

        adresse_text = tk.Text(adresse_frame, width=30, height=3, wrap=tk.WORD)
        adresse_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        adresse_scrollbar = ttk.Scrollbar(adresse_frame, orient=tk.VERTICAL, command=adresse_text.yview)
        adresse_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        adresse_text.configure(yscrollcommand=adresse_scrollbar.set)

        adresse_text.insert('1.0', adresse_var.get())

        # Boutons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=5, column=0, columnspan=2, pady=20)

        def save_client():
            # Validation
            if not nom_var.get().strip() or not prenom_var.get().strip():
                messagebox.showerror("Erreur", "Le nom et le prénom sont obligatoires.")
                return

            # Récupérer les données
            nom = nom_var.get().strip()
            prenom = prenom_var.get().strip()
            telephone = telephone_var.get().strip()
            email = email_var.get().strip()
            adresse = adresse_text.get('1.0', tk.END).strip()

            try:
                if mode == 'add':
                    self.db.execute_query(
                        "INSERT INTO clients (nom, prenom, telephone, email, adresse) VALUES (?, ?, ?, ?, ?)",
                        (nom, prenom, telephone, email, adresse)
                    )
                    messagebox.showinfo("Succès", "Client ajouté avec succès.")
                else:
                    self.db.execute_query(
                        "UPDATE clients SET nom=?, prenom=?, telephone=?, email=?, adresse=? WHERE id=?",
                        (nom, prenom, telephone, email, adresse, client_id)
                    )
                    messagebox.showinfo("Succès", "Client modifié avec succès.")

                self.load_clients()
                window.destroy()

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        ttk.Button(btn_frame, text="Enregistrer", command=save_client).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Annuler", command=window.destroy).grid(row=0, column=1)

        # Focus sur le premier champ
        nom_entry.focus()

    def show_biens(self):
        """Affiche la gestion des biens"""
        self.clear_content()

        biens_frame = ttk.LabelFrame(self.content_frame, text="Gestion des Biens", padding="10")
        biens_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        biens_frame.columnconfigure(0, weight=1)
        biens_frame.rowconfigure(1, weight=1)

        # Boutons d'action
        btn_frame = ttk.Frame(biens_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="Ajouter Bien", command=self.add_bien).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Modifier Bien", command=self.edit_bien).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(btn_frame, text="Supprimer Bien", command=self.delete_bien).grid(row=0, column=2)

        # Liste des biens
        self.biens_tree = ttk.Treeview(biens_frame, columns=('ID', 'Type', 'Adresse', 'Superficie', 'Pièces', 'Loyer', 'Disponible'), show='headings')
        self.biens_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration des colonnes
        self.biens_tree.heading('ID', text='ID')
        self.biens_tree.heading('Type', text='Type')
        self.biens_tree.heading('Adresse', text='Adresse')
        self.biens_tree.heading('Superficie', text='Superficie (m²)')
        self.biens_tree.heading('Pièces', text='Pièces')
        self.biens_tree.heading('Loyer', text='Loyer (€)')
        self.biens_tree.heading('Disponible', text='Disponible')

        self.biens_tree.column('ID', width=50)
        self.biens_tree.column('Type', width=100)
        self.biens_tree.column('Adresse', width=200)
        self.biens_tree.column('Superficie', width=100)
        self.biens_tree.column('Pièces', width=80)
        self.biens_tree.column('Loyer', width=100)
        self.biens_tree.column('Disponible', width=80)

        # Scrollbars
        scrollbar_biens_v = ttk.Scrollbar(biens_frame, orient=tk.VERTICAL, command=self.biens_tree.yview)
        scrollbar_biens_v.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.biens_tree.configure(yscrollcommand=scrollbar_biens_v.set)

        scrollbar_biens_h = ttk.Scrollbar(biens_frame, orient=tk.HORIZONTAL, command=self.biens_tree.xview)
        scrollbar_biens_h.grid(row=2, column=0, sticky=(tk.W, tk.E))
        self.biens_tree.configure(xscrollcommand=scrollbar_biens_h.set)

        # Charger les données
        self.load_biens()

    def load_biens(self):
        """Charge la liste des biens"""
        # Effacer les données existantes
        for item in self.biens_tree.get_children():
            self.biens_tree.delete(item)

        # Charger les biens depuis la base de données
        biens = self.db.execute_query("""
            SELECT id, type_bien, adresse, superficie, nb_pieces, loyer_mensuel, disponible
            FROM biens ORDER BY adresse
        """)

        for bien in biens:
            disponible_text = "Oui" if bien[6] else "Non"
            values = list(bien[:-1]) + [disponible_text]
            self.biens_tree.insert('', 'end', values=values)

    def add_bien(self):
        """Ouvre la fenêtre d'ajout de bien"""
        self.bien_form_window(mode='add')

    def edit_bien(self):
        """Ouvre la fenêtre de modification de bien"""
        selected = self.biens_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un bien à modifier.")
            return

        bien_id = self.biens_tree.item(selected[0])['values'][0]
        self.bien_form_window(mode='edit', bien_id=bien_id)

    def delete_bien(self):
        """Supprime un bien"""
        selected = self.biens_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un bien à supprimer.")
            return

        bien_id = self.biens_tree.item(selected[0])['values'][0]
        bien_adresse = self.biens_tree.item(selected[0])['values'][2]

        if messagebox.askyesno("Confirmation", f"Êtes-vous sûr de vouloir supprimer le bien à l'adresse {bien_adresse} ?"):
            self.db.execute_query("DELETE FROM biens WHERE id = ?", (bien_id,))
            self.load_biens()
            messagebox.showinfo("Succès", "Bien supprimé avec succès.")

    def bien_form_window(self, mode='add', bien_id=None):
        """Fenêtre de formulaire pour ajouter/modifier un bien"""
        window = tk.Toplevel(self.root)
        window.title("Ajouter Bien" if mode == 'add' else "Modifier Bien")
        window.geometry("450x400")
        window.resizable(False, False)

        # Centrer la fenêtre
        window.transient(self.root)
        window.grab_set()

        # Frame principal
        main_frame = ttk.Frame(window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Variables pour les champs
        type_var = tk.StringVar()
        adresse_var = tk.StringVar()
        superficie_var = tk.StringVar()
        pieces_var = tk.StringVar()
        loyer_var = tk.StringVar()
        charges_var = tk.StringVar()
        disponible_var = tk.BooleanVar(value=True)
        description_var = tk.StringVar()

        # Si mode modification, charger les données existantes
        if mode == 'edit' and bien_id:
            bien_data = self.db.execute_query("""
                SELECT type_bien, adresse, superficie, nb_pieces, loyer_mensuel, charges, disponible, description
                FROM biens WHERE id = ?
            """, (bien_id,))
            if bien_data:
                type_var.set(bien_data[0][0] or '')
                adresse_var.set(bien_data[0][1] or '')
                superficie_var.set(str(bien_data[0][2]) if bien_data[0][2] else '')
                pieces_var.set(str(bien_data[0][3]) if bien_data[0][3] else '')
                loyer_var.set(str(bien_data[0][4]) if bien_data[0][4] else '')
                charges_var.set(str(bien_data[0][5]) if bien_data[0][5] else '')
                disponible_var.set(bool(bien_data[0][6]))
                description_var.set(bien_data[0][7] or '')

        # Champs du formulaire
        row = 0

        ttk.Label(main_frame, text="Type de bien *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        type_combo = ttk.Combobox(main_frame, textvariable=type_var, width=27)
        type_combo['values'] = ('Appartement', 'Maison', 'Studio', 'Loft', 'Bureau', 'Local commercial')
        type_combo.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Adresse *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        adresse_entry = ttk.Entry(main_frame, textvariable=adresse_var, width=30)
        adresse_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Superficie (m²):").grid(row=row, column=0, sticky=tk.W, pady=5)
        superficie_entry = ttk.Entry(main_frame, textvariable=superficie_var, width=30)
        superficie_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Nombre de pièces:").grid(row=row, column=0, sticky=tk.W, pady=5)
        pieces_entry = ttk.Entry(main_frame, textvariable=pieces_var, width=30)
        pieces_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Loyer mensuel (€) *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        loyer_entry = ttk.Entry(main_frame, textvariable=loyer_var, width=30)
        loyer_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Charges (€):").grid(row=row, column=0, sticky=tk.W, pady=5)
        charges_entry = ttk.Entry(main_frame, textvariable=charges_var, width=30)
        charges_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Checkbutton(main_frame, text="Disponible", variable=disponible_var).grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        row += 1

        ttk.Label(main_frame, text="Description:").grid(row=row, column=0, sticky=tk.W, pady=5)

        # Frame pour le texte avec scrollbar
        description_frame = ttk.Frame(main_frame)
        description_frame.grid(row=row, column=1, pady=5, padx=(10, 0))

        description_text = tk.Text(description_frame, width=30, height=3, wrap=tk.WORD)
        description_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        description_scrollbar = ttk.Scrollbar(description_frame, orient=tk.VERTICAL, command=description_text.yview)
        description_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        description_text.configure(yscrollcommand=description_scrollbar.set)

        description_text.insert('1.0', description_var.get())
        row += 1

        # Boutons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=row, column=0, columnspan=2, pady=20)

        def save_bien():
            # Validation
            if not type_var.get().strip() or not adresse_var.get().strip() or not loyer_var.get().strip():
                messagebox.showerror("Erreur", "Le type, l'adresse et le loyer sont obligatoires.")
                return

            try:
                # Validation des nombres
                superficie = float(superficie_var.get()) if superficie_var.get().strip() else None
                pieces = int(pieces_var.get()) if pieces_var.get().strip() else None
                loyer = float(loyer_var.get())
                charges = float(charges_var.get()) if charges_var.get().strip() else 0

                # Récupérer les données
                type_bien = type_var.get().strip()
                adresse = adresse_var.get().strip()
                disponible = disponible_var.get()
                description = description_text.get('1.0', tk.END).strip()

                if mode == 'add':
                    self.db.execute_query("""
                        INSERT INTO biens (type_bien, adresse, superficie, nb_pieces, loyer_mensuel, charges, disponible, description)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (type_bien, adresse, superficie, pieces, loyer, charges, disponible, description))
                    messagebox.showinfo("Succès", "Bien ajouté avec succès.")
                else:
                    self.db.execute_query("""
                        UPDATE biens SET type_bien=?, adresse=?, superficie=?, nb_pieces=?, loyer_mensuel=?,
                        charges=?, disponible=?, description=? WHERE id=?
                    """, (type_bien, adresse, superficie, pieces, loyer, charges, disponible, description, bien_id))
                    messagebox.showinfo("Succès", "Bien modifié avec succès.")

                self.load_biens()
                window.destroy()

            except ValueError:
                messagebox.showerror("Erreur", "Veuillez entrer des valeurs numériques valides pour la superficie, les pièces et le loyer.")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        ttk.Button(btn_frame, text="Enregistrer", command=save_bien).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Annuler", command=window.destroy).grid(row=0, column=1)

        # Focus sur le premier champ
        type_combo.focus()

    def show_voitures(self):
        """Affiche la gestion des voitures"""
        self.clear_content()

        voitures_frame = ttk.LabelFrame(self.content_frame, text="Gestion des Voitures", padding="10")
        voitures_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        voitures_frame.columnconfigure(0, weight=1)
        voitures_frame.rowconfigure(1, weight=1)

        # Boutons d'action
        btn_frame = ttk.Frame(voitures_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="Ajouter Voiture", command=self.add_voiture).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Modifier Voiture", command=self.edit_voiture).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(btn_frame, text="Supprimer Voiture", command=self.delete_voiture).grid(row=0, column=2)

        # Liste des voitures
        self.voitures_tree = ttk.Treeview(voitures_frame, columns=('ID', 'Marque', 'Modèle', 'Année', 'Immatriculation', 'Prix/jour', 'Disponible'), show='headings')
        self.voitures_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration des colonnes
        self.voitures_tree.heading('ID', text='ID')
        self.voitures_tree.heading('Marque', text='Marque')
        self.voitures_tree.heading('Modèle', text='Modèle')
        self.voitures_tree.heading('Année', text='Année')
        self.voitures_tree.heading('Immatriculation', text='Immatriculation')
        self.voitures_tree.heading('Prix/jour', text='Prix/jour (€)')
        self.voitures_tree.heading('Disponible', text='Disponible')

        self.voitures_tree.column('ID', width=50)
        self.voitures_tree.column('Marque', width=100)
        self.voitures_tree.column('Modèle', width=120)
        self.voitures_tree.column('Année', width=80)
        self.voitures_tree.column('Immatriculation', width=120)
        self.voitures_tree.column('Prix/jour', width=100)
        self.voitures_tree.column('Disponible', width=80)

        # Scrollbars
        scrollbar_voitures_v = ttk.Scrollbar(voitures_frame, orient=tk.VERTICAL, command=self.voitures_tree.yview)
        scrollbar_voitures_v.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.voitures_tree.configure(yscrollcommand=scrollbar_voitures_v.set)

        scrollbar_voitures_h = ttk.Scrollbar(voitures_frame, orient=tk.HORIZONTAL, command=self.voitures_tree.xview)
        scrollbar_voitures_h.grid(row=2, column=0, sticky=(tk.W, tk.E))
        self.voitures_tree.configure(xscrollcommand=scrollbar_voitures_h.set)

        # Charger les données
        self.load_voitures()

    def load_voitures(self):
        """Charge la liste des voitures"""
        # Effacer les données existantes
        for item in self.voitures_tree.get_children():
            self.voitures_tree.delete(item)

        # Charger les voitures depuis la base de données
        voitures = self.db.execute_query("""
            SELECT id, marque, modele, annee, immatriculation, prix_jour, disponible
            FROM voitures ORDER BY marque, modele
        """)

        for voiture in voitures:
            disponible_text = "Oui" if voiture[6] else "Non"
            values = list(voiture[:-1]) + [disponible_text]
            self.voitures_tree.insert('', 'end', values=values)

    def add_voiture(self):
        """Ouvre la fenêtre d'ajout de voiture"""
        self.voiture_form_window(mode='add')

    def edit_voiture(self):
        """Ouvre la fenêtre de modification de voiture"""
        selected = self.voitures_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner une voiture à modifier.")
            return

        voiture_id = self.voitures_tree.item(selected[0])['values'][0]
        self.voiture_form_window(mode='edit', voiture_id=voiture_id)

    def delete_voiture(self):
        """Supprime une voiture"""
        selected = self.voitures_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner une voiture à supprimer.")
            return

        voiture_id = self.voitures_tree.item(selected[0])['values'][0]
        voiture_info = f"{self.voitures_tree.item(selected[0])['values'][1]} {self.voitures_tree.item(selected[0])['values'][2]}"

        if messagebox.askyesno("Confirmation", f"Êtes-vous sûr de vouloir supprimer la voiture {voiture_info} ?"):
            self.db.execute_query("DELETE FROM voitures WHERE id = ?", (voiture_id,))
            self.load_voitures()
            messagebox.showinfo("Succès", "Voiture supprimée avec succès.")

    def voiture_form_window(self, mode='add', voiture_id=None):
        """Fenêtre de formulaire pour ajouter/modifier une voiture"""
        window = tk.Toplevel(self.root)
        window.title("Ajouter Voiture" if mode == 'add' else "Modifier Voiture")
        window.geometry("450x500")
        window.resizable(False, False)

        # Centrer la fenêtre
        window.transient(self.root)
        window.grab_set()

        # Frame principal
        main_frame = ttk.Frame(window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Variables pour les champs
        marque_var = tk.StringVar()
        modele_var = tk.StringVar()
        annee_var = tk.StringVar()
        immatriculation_var = tk.StringVar()
        couleur_var = tk.StringVar()
        carburant_var = tk.StringVar()
        transmission_var = tk.StringVar()
        kilometrage_var = tk.StringVar()
        prix_jour_var = tk.StringVar()
        disponible_var = tk.BooleanVar(value=True)
        description_var = tk.StringVar()

        # Si mode modification, charger les données existantes
        if mode == 'edit' and voiture_id:
            voiture_data = self.db.execute_query("""
                SELECT marque, modele, annee, immatriculation, couleur, carburant, transmission,
                       kilometrage, prix_jour, disponible, description
                FROM voitures WHERE id = ?
            """, (voiture_id,))
            if voiture_data:
                marque_var.set(voiture_data[0][0] or '')
                modele_var.set(voiture_data[0][1] or '')
                annee_var.set(str(voiture_data[0][2]) if voiture_data[0][2] else '')
                immatriculation_var.set(voiture_data[0][3] or '')
                couleur_var.set(voiture_data[0][4] or '')
                carburant_var.set(voiture_data[0][5] or '')
                transmission_var.set(voiture_data[0][6] or '')
                kilometrage_var.set(str(voiture_data[0][7]) if voiture_data[0][7] else '0')
                prix_jour_var.set(str(voiture_data[0][8]) if voiture_data[0][8] else '')
                disponible_var.set(bool(voiture_data[0][9]))
                description_var.set(voiture_data[0][10] or '')

        # Champs du formulaire
        row = 0

        ttk.Label(main_frame, text="Marque *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        marque_entry = ttk.Entry(main_frame, textvariable=marque_var, width=30)
        marque_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Modèle *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        modele_entry = ttk.Entry(main_frame, textvariable=modele_var, width=30)
        modele_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Année:").grid(row=row, column=0, sticky=tk.W, pady=5)
        annee_entry = ttk.Entry(main_frame, textvariable=annee_var, width=30)
        annee_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Immatriculation *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        immatriculation_entry = ttk.Entry(main_frame, textvariable=immatriculation_var, width=30)
        immatriculation_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Couleur:").grid(row=row, column=0, sticky=tk.W, pady=5)
        couleur_entry = ttk.Entry(main_frame, textvariable=couleur_var, width=30)
        couleur_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Carburant:").grid(row=row, column=0, sticky=tk.W, pady=5)
        carburant_combo = ttk.Combobox(main_frame, textvariable=carburant_var, width=27)
        carburant_combo['values'] = ('Essence', 'Diesel', 'Électrique', 'Hybride', 'GPL')
        carburant_combo.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Transmission:").grid(row=row, column=0, sticky=tk.W, pady=5)
        transmission_combo = ttk.Combobox(main_frame, textvariable=transmission_var, width=27)
        transmission_combo['values'] = ('Manuelle', 'Automatique')
        transmission_combo.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Kilométrage:").grid(row=row, column=0, sticky=tk.W, pady=5)
        kilometrage_entry = ttk.Entry(main_frame, textvariable=kilometrage_var, width=30)
        kilometrage_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Prix par jour (€) *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        prix_jour_entry = ttk.Entry(main_frame, textvariable=prix_jour_var, width=30)
        prix_jour_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Checkbutton(main_frame, text="Disponible", variable=disponible_var).grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        row += 1

        ttk.Label(main_frame, text="Description:").grid(row=row, column=0, sticky=tk.W, pady=5)

        # Frame pour le texte avec scrollbar
        description_frame = ttk.Frame(main_frame)
        description_frame.grid(row=row, column=1, pady=5, padx=(10, 0))

        description_text = tk.Text(description_frame, width=30, height=3, wrap=tk.WORD)
        description_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        description_scrollbar = ttk.Scrollbar(description_frame, orient=tk.VERTICAL, command=description_text.yview)
        description_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        description_text.configure(yscrollcommand=description_scrollbar.set)

        description_text.insert('1.0', description_var.get())
        row += 1

        # Boutons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=row, column=0, columnspan=2, pady=20)

        def save_voiture():
            # Validation
            if not marque_var.get().strip() or not modele_var.get().strip() or not immatriculation_var.get().strip() or not prix_jour_var.get().strip():
                messagebox.showerror("Erreur", "La marque, le modèle, l'immatriculation et le prix par jour sont obligatoires.")
                return

            try:
                # Validation des nombres
                annee = int(annee_var.get()) if annee_var.get().strip() else None
                kilometrage = int(kilometrage_var.get()) if kilometrage_var.get().strip() else 0
                prix_jour = float(prix_jour_var.get())

                # Récupérer les données
                marque = marque_var.get().strip()
                modele = modele_var.get().strip()
                immatriculation = immatriculation_var.get().strip().upper()
                couleur = couleur_var.get().strip()
                carburant = carburant_var.get().strip()
                transmission = transmission_var.get().strip()
                disponible = disponible_var.get()
                description = description_text.get('1.0', tk.END).strip()

                if mode == 'add':
                    self.db.execute_query("""
                        INSERT INTO voitures (marque, modele, annee, immatriculation, couleur, carburant,
                                            transmission, kilometrage, prix_jour, disponible, description)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (marque, modele, annee, immatriculation, couleur, carburant, transmission,
                          kilometrage, prix_jour, disponible, description))
                    messagebox.showinfo("Succès", "Voiture ajoutée avec succès.")
                else:
                    self.db.execute_query("""
                        UPDATE voitures SET marque=?, modele=?, annee=?, immatriculation=?, couleur=?,
                        carburant=?, transmission=?, kilometrage=?, prix_jour=?, disponible=?, description=?
                        WHERE id=?
                    """, (marque, modele, annee, immatriculation, couleur, carburant, transmission,
                          kilometrage, prix_jour, disponible, description, voiture_id))
                    messagebox.showinfo("Succès", "Voiture modifiée avec succès.")

                self.load_voitures()
                window.destroy()

            except ValueError:
                messagebox.showerror("Erreur", "Veuillez entrer des valeurs numériques valides pour l'année, le kilométrage et le prix.")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        ttk.Button(btn_frame, text="Enregistrer", command=save_voiture).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Annuler", command=window.destroy).grid(row=0, column=1)

        # Focus sur le premier champ
        marque_entry.focus()

    def show_contrats(self):
        """Affiche la gestion des contrats"""
        self.clear_content()

        contrats_frame = ttk.LabelFrame(self.content_frame, text="Gestion des Contrats", padding="10")
        contrats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        contrats_frame.columnconfigure(0, weight=1)
        contrats_frame.rowconfigure(1, weight=1)

        # Boutons d'action
        btn_frame = ttk.Frame(contrats_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="Nouveau Contrat", command=self.add_contrat).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Modifier Contrat", command=self.edit_contrat).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(btn_frame, text="Résilier Contrat", command=self.terminate_contrat).grid(row=0, column=2)

        # Liste des contrats
        self.contrats_tree = ttk.Treeview(contrats_frame, columns=('ID', 'Type', 'Client', 'Objet', 'Début', 'Fin', 'Prix', 'Statut'), show='headings')
        self.contrats_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration des colonnes
        self.contrats_tree.heading('ID', text='ID')
        self.contrats_tree.heading('Type', text='Type')
        self.contrats_tree.heading('Client', text='Client')
        self.contrats_tree.heading('Objet', text='Bien/Voiture')
        self.contrats_tree.heading('Début', text='Date début')
        self.contrats_tree.heading('Fin', text='Date fin')
        self.contrats_tree.heading('Prix', text='Prix (€)')
        self.contrats_tree.heading('Statut', text='Statut')

        self.contrats_tree.column('ID', width=50)
        self.contrats_tree.column('Type', width=80)
        self.contrats_tree.column('Client', width=120)
        self.contrats_tree.column('Objet', width=180)
        self.contrats_tree.column('Début', width=100)
        self.contrats_tree.column('Fin', width=100)
        self.contrats_tree.column('Prix', width=100)
        self.contrats_tree.column('Statut', width=80)

        # Scrollbars
        scrollbar_contrats_v = ttk.Scrollbar(contrats_frame, orient=tk.VERTICAL, command=self.contrats_tree.yview)
        scrollbar_contrats_v.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.contrats_tree.configure(yscrollcommand=scrollbar_contrats_v.set)

        scrollbar_contrats_h = ttk.Scrollbar(contrats_frame, orient=tk.HORIZONTAL, command=self.contrats_tree.xview)
        scrollbar_contrats_h.grid(row=2, column=0, sticky=(tk.W, tk.E))
        self.contrats_tree.configure(xscrollcommand=scrollbar_contrats_h.set)

        # Charger les données
        self.load_contrats()

    def load_contrats(self):
        """Charge la liste des contrats"""
        # Effacer les données existantes
        for item in self.contrats_tree.get_children():
            self.contrats_tree.delete(item)

        # Charger les contrats de biens
        contrats_biens = self.db.execute_query("""
            SELECT c.id, 'Bien' as type, cl.prenom || ' ' || cl.nom as client, b.adresse as objet,
                   c.date_debut, c.date_fin, c.prix_unitaire, c.statut
            FROM contrats c
            JOIN clients cl ON c.client_id = cl.id
            JOIN biens b ON c.bien_id = b.id
            WHERE c.type_location = 'bien'
            ORDER BY c.date_creation DESC
        """)

        # Charger les contrats de voitures
        contrats_voitures = self.db.execute_query("""
            SELECT c.id, 'Voiture' as type, cl.prenom || ' ' || cl.nom as client,
                   v.marque || ' ' || v.modele || ' (' || v.immatriculation || ')' as objet,
                   c.date_debut, c.date_fin, c.prix_unitaire, c.statut
            FROM contrats c
            JOIN clients cl ON c.client_id = cl.id
            JOIN voitures v ON c.voiture_id = v.id
            WHERE c.type_location = 'voiture'
            ORDER BY c.date_creation DESC
        """)

        # Combiner et trier tous les contrats
        tous_contrats = contrats_biens + contrats_voitures
        tous_contrats.sort(key=lambda x: x[0], reverse=True)  # Trier par ID décroissant

        for contrat in tous_contrats:
            self.contrats_tree.insert('', 'end', values=contrat)

    def add_contrat(self):
        """Ouvre la fenêtre d'ajout de contrat"""
        self.contrat_form_window(mode='add')

    def edit_contrat(self):
        """Ouvre la fenêtre de modification de contrat"""
        selected = self.contrats_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un contrat à modifier.")
            return

        contrat_id = self.contrats_tree.item(selected[0])['values'][0]
        self.contrat_form_window(mode='edit', contrat_id=contrat_id)

    def terminate_contrat(self):
        """Résilie un contrat"""
        selected = self.contrats_tree.selection()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un contrat à résilier.")
            return

        contrat_id = self.contrats_tree.item(selected[0])['values'][0]
        client_nom = self.contrats_tree.item(selected[0])['values'][2]

        if messagebox.askyesno("Confirmation", f"Êtes-vous sûr de vouloir résilier le contrat de {client_nom} ?"):
            self.db.execute_query("UPDATE contrats SET statut = 'résilié', date_fin = ? WHERE id = ?",
                                (date.today().isoformat(), contrat_id))

            # Rendre l'objet disponible selon le type
            contrat_info = self.db.execute_query("SELECT type_location, bien_id, voiture_id FROM contrats WHERE id = ?", (contrat_id,))[0]
            if contrat_info[0] == 'bien' and contrat_info[1]:
                self.db.execute_query("UPDATE biens SET disponible = 1 WHERE id = ?", (contrat_info[1],))
            elif contrat_info[0] == 'voiture' and contrat_info[2]:
                self.db.execute_query("UPDATE voitures SET disponible = 1 WHERE id = ?", (contrat_info[2],))

            self.load_contrats()
            messagebox.showinfo("Succès", "Contrat résilié avec succès.")

    def contrat_form_window(self, mode='add', contrat_id=None):
        """Fenêtre de formulaire pour ajouter/modifier un contrat"""
        window = tk.Toplevel(self.root)
        window.title("Nouveau Contrat" if mode == 'add' else "Modifier Contrat")
        window.geometry("450x350")
        window.resizable(False, False)

        # Centrer la fenêtre
        window.transient(self.root)
        window.grab_set()

        # Frame principal
        main_frame = ttk.Frame(window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Variables pour les champs
        type_location_var = tk.StringVar(value="bien")
        client_var = tk.StringVar()
        objet_var = tk.StringVar()
        date_debut_var = tk.StringVar(value=date.today().isoformat())
        date_fin_var = tk.StringVar()
        prix_var = tk.StringVar()
        depot_var = tk.StringVar()

        # Récupérer les listes de clients, biens et voitures
        clients = self.db.execute_query("SELECT id, prenom || ' ' || nom FROM clients ORDER BY nom, prenom")
        biens_disponibles = self.db.execute_query("SELECT id, type_bien || ' - ' || adresse FROM biens WHERE disponible = 1")
        voitures_disponibles = self.db.execute_query("SELECT id, marque || ' ' || modele || ' (' || immatriculation || ')' FROM voitures WHERE disponible = 1")

        # Si mode modification, charger les données existantes
        if mode == 'edit' and contrat_id:
            contrat_data = self.db.execute_query("""
                SELECT client_id, bien_id, voiture_id, type_location, date_debut, date_fin, prix_unitaire, depot_garantie
                FROM contrats WHERE id = ?
            """, (contrat_id,))
            if contrat_data:
                # Définir le type de location
                type_location_var.set(contrat_data[0][3])

                # Trouver le client correspondant
                for client_id, client_nom in clients:
                    if client_id == contrat_data[0][0]:
                        client_var.set(f"{client_id} - {client_nom}")
                        break

                # Trouver l'objet correspondant (bien ou voiture)
                if contrat_data[0][3] == 'bien' and contrat_data[0][1]:
                    tous_biens = self.db.execute_query("SELECT id, type_bien || ' - ' || adresse FROM biens")
                    for bien_id, bien_nom in tous_biens:
                        if bien_id == contrat_data[0][1]:
                            objet_var.set(f"{bien_id} - {bien_nom}")
                            break
                elif contrat_data[0][3] == 'voiture' and contrat_data[0][2]:
                    toutes_voitures = self.db.execute_query("SELECT id, marque || ' ' || modele || ' (' || immatriculation || ')' FROM voitures")
                    for voiture_id, voiture_nom in toutes_voitures:
                        if voiture_id == contrat_data[0][2]:
                            objet_var.set(f"{voiture_id} - {voiture_nom}")
                            break

                date_debut_var.set(contrat_data[0][4] or '')
                date_fin_var.set(contrat_data[0][5] or '')
                prix_var.set(str(contrat_data[0][6]) if contrat_data[0][6] else '')
                depot_var.set(str(contrat_data[0][7]) if contrat_data[0][7] else '')

        # Champs du formulaire
        row = 0

        ttk.Label(main_frame, text="Type de location *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        type_frame = ttk.Frame(main_frame)
        type_frame.grid(row=row, column=1, pady=5, padx=(10, 0), sticky=tk.W)
        ttk.Radiobutton(type_frame, text="Bien immobilier", variable=type_location_var, value="bien").grid(row=0, column=0, padx=(0, 20))
        ttk.Radiobutton(type_frame, text="Voiture", variable=type_location_var, value="voiture").grid(row=0, column=1)
        row += 1

        ttk.Label(main_frame, text="Client *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        client_combo = ttk.Combobox(main_frame, textvariable=client_var, width=35)
        client_combo['values'] = [f"{c[0]} - {c[1]}" for c in clients]
        client_combo.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Objet à louer *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        objet_combo = ttk.Combobox(main_frame, textvariable=objet_var, width=35)
        objet_combo.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        # Fonction pour mettre à jour la liste des objets selon le type
        def update_objet_list(*args):
            if type_location_var.get() == "bien":
                if mode == 'add':
                    objet_combo['values'] = [f"{b[0]} - {b[1]}" for b in biens_disponibles]
                else:
                    tous_biens = self.db.execute_query("SELECT id, type_bien || ' - ' || adresse FROM biens")
                    objet_combo['values'] = [f"{b[0]} - {b[1]}" for b in tous_biens]
            else:  # voiture
                if mode == 'add':
                    objet_combo['values'] = [f"{v[0]} - {v[1]}" for v in voitures_disponibles]
                else:
                    toutes_voitures = self.db.execute_query("SELECT id, marque || ' ' || modele || ' (' || immatriculation || ')' FROM voitures")
                    objet_combo['values'] = [f"{v[0]} - {v[1]}" for v in toutes_voitures]
            objet_combo.set('')  # Réinitialiser la sélection

        # Lier la fonction au changement de type
        type_location_var.trace('w', update_objet_list)
        update_objet_list()  # Initialiser la liste

        ttk.Label(main_frame, text="Date début *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        date_debut_entry = ttk.Entry(main_frame, textvariable=date_debut_var, width=37)
        date_debut_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Date fin:").grid(row=row, column=0, sticky=tk.W, pady=5)
        date_fin_entry = ttk.Entry(main_frame, textvariable=date_fin_var, width=37)
        date_fin_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Prix (€/mois ou €/jour) *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        prix_entry = ttk.Entry(main_frame, textvariable=prix_var, width=37)
        prix_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        ttk.Label(main_frame, text="Dépôt de garantie (€):").grid(row=row, column=0, sticky=tk.W, pady=5)
        depot_entry = ttk.Entry(main_frame, textvariable=depot_var, width=37)
        depot_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1

        # Boutons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=row, column=0, columnspan=2, pady=20)

        def save_contrat():
            # Validation
            if not client_var.get().strip() or not objet_var.get().strip() or not date_debut_var.get().strip() or not prix_var.get().strip():
                messagebox.showerror("Erreur", "Le client, l'objet, la date de début et le prix sont obligatoires.")
                return

            try:
                # Extraire les IDs
                client_id = int(client_var.get().split(' - ')[0])
                objet_id = int(objet_var.get().split(' - ')[0])
                type_location = type_location_var.get()

                # Validation des données
                date_debut = date_debut_var.get().strip()
                date_fin = date_fin_var.get().strip() if date_fin_var.get().strip() else None
                prix = float(prix_var.get())
                depot = float(depot_var.get()) if depot_var.get().strip() else None

                if mode == 'add':
                    if type_location == 'bien':
                        self.db.execute_query("""
                            INSERT INTO contrats (client_id, bien_id, type_location, date_debut, date_fin, prix_unitaire, depot_garantie)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        """, (client_id, objet_id, type_location, date_debut, date_fin, prix, depot))

                        # Marquer le bien comme non disponible
                        self.db.execute_query("UPDATE biens SET disponible = 0 WHERE id = ?", (objet_id,))
                    else:  # voiture
                        self.db.execute_query("""
                            INSERT INTO contrats (client_id, voiture_id, type_location, date_debut, date_fin, prix_unitaire, depot_garantie)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        """, (client_id, objet_id, type_location, date_debut, date_fin, prix, depot))

                        # Marquer la voiture comme non disponible
                        self.db.execute_query("UPDATE voitures SET disponible = 0 WHERE id = ?", (objet_id,))

                    messagebox.showinfo("Succès", "Contrat créé avec succès.")
                else:
                    if type_location == 'bien':
                        self.db.execute_query("""
                            UPDATE contrats SET client_id=?, bien_id=?, voiture_id=NULL, type_location=?,
                            date_debut=?, date_fin=?, prix_unitaire=?, depot_garantie=? WHERE id=?
                        """, (client_id, objet_id, type_location, date_debut, date_fin, prix, depot, contrat_id))
                    else:  # voiture
                        self.db.execute_query("""
                            UPDATE contrats SET client_id=?, bien_id=NULL, voiture_id=?, type_location=?,
                            date_debut=?, date_fin=?, prix_unitaire=?, depot_garantie=? WHERE id=?
                        """, (client_id, objet_id, type_location, date_debut, date_fin, prix, depot, contrat_id))

                    messagebox.showinfo("Succès", "Contrat modifié avec succès.")

                self.load_contrats()
                window.destroy()

            except ValueError:
                messagebox.showerror("Erreur", "Veuillez entrer des valeurs valides.")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")

        ttk.Button(btn_frame, text="Enregistrer", command=save_contrat).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(btn_frame, text="Annuler", command=window.destroy).grid(row=0, column=1)

        # Focus sur le premier champ
        client_combo.focus()

    def show_reports(self):
        """Affiche les rapports"""
        self.clear_content()

        # Container principal pour les rapports
        reports_container = ttk.Frame(self.content_frame)
        reports_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        reports_container.columnconfigure(0, weight=1)
        reports_container.rowconfigure(0, weight=1)

        # Canvas pour le scroll des rapports
        reports_canvas = tk.Canvas(reports_container, highlightthickness=0)
        reports_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbar pour les rapports
        reports_scrollbar = ttk.Scrollbar(reports_container, orient=tk.VERTICAL, command=reports_canvas.yview)
        reports_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        reports_canvas.configure(yscrollcommand=reports_scrollbar.set)

        # Frame de contenu des rapports
        reports_frame = ttk.LabelFrame(reports_canvas, text="Rapports et Statistiques", padding="20")
        reports_window = reports_canvas.create_window((0, 0), window=reports_frame, anchor="nw")

        # Configurer le scroll pour les rapports
        def configure_reports_scroll(event=None):
            reports_canvas.configure(scrollregion=reports_canvas.bbox("all"))

        def configure_reports_width(event):
            canvas_width = event.width
            reports_canvas.itemconfig(reports_window, width=canvas_width)

        reports_frame.bind('<Configure>', configure_reports_scroll)
        reports_canvas.bind('<Configure>', configure_reports_width)

        # Scroll avec la molette pour les rapports
        def on_reports_mousewheel(event):
            reports_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        reports_canvas.bind("<MouseWheel>", on_reports_mousewheel)

        reports_frame.columnconfigure(0, weight=1)

        # Statistiques détaillées
        stats_frame = ttk.LabelFrame(reports_frame, text="Statistiques générales", padding="15")
        stats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # Calculer les statistiques
        total_clients = len(self.db.execute_query("SELECT * FROM clients"))
        total_biens = len(self.db.execute_query("SELECT * FROM biens"))
        biens_loues = len(self.db.execute_query("SELECT * FROM biens WHERE disponible = 0"))
        biens_disponibles = total_biens - biens_loues

        total_voitures = len(self.db.execute_query("SELECT * FROM voitures"))
        voitures_louees = len(self.db.execute_query("SELECT * FROM voitures WHERE disponible = 0"))
        voitures_disponibles = total_voitures - voitures_louees

        contrats_actifs = len(self.db.execute_query("SELECT * FROM contrats WHERE statut = 'actif'"))
        contrats_biens = len(self.db.execute_query("SELECT * FROM contrats WHERE statut = 'actif' AND type_location = 'bien'"))
        contrats_voitures = len(self.db.execute_query("SELECT * FROM contrats WHERE statut = 'actif' AND type_location = 'voiture'"))

        # Revenus mensuels des biens
        revenus_biens_result = self.db.execute_query("""
            SELECT SUM(prix_unitaire) FROM contrats
            WHERE statut = 'actif' AND type_location = 'bien'
        """)
        revenus_biens = revenus_biens_result[0][0] if revenus_biens_result[0][0] else 0

        # Revenus journaliers des voitures (estimation mensuelle)
        revenus_voitures_result = self.db.execute_query("""
            SELECT SUM(prix_unitaire * 30) FROM contrats
            WHERE statut = 'actif' AND type_location = 'voiture'
        """)
        revenus_voitures = revenus_voitures_result[0][0] if revenus_voitures_result[0][0] else 0

        revenus_totaux = revenus_biens + revenus_voitures

        stats_text = f"""
Nombre total de clients: {total_clients}

BIENS IMMOBILIERS:
Nombre total de biens: {total_biens}
Biens loués: {biens_loues}
Biens disponibles: {biens_disponibles}
Taux d'occupation biens: {(biens_loues/total_biens*100) if total_biens > 0 else 0:.1f}%

VOITURES:
Nombre total de voitures: {total_voitures}
Voitures louées: {voitures_louees}
Voitures disponibles: {voitures_disponibles}
Taux d'occupation voitures: {(voitures_louees/total_voitures*100) if total_voitures > 0 else 0:.1f}%

CONTRATS:
Contrats actifs total: {contrats_actifs}
Contrats biens: {contrats_biens}
Contrats voitures: {contrats_voitures}

REVENUS MENSUELS:
Revenus biens: {revenus_biens:.2f} €
Revenus voitures (estimé): {revenus_voitures:.2f} €
Revenus totaux: {revenus_totaux:.2f} €
        """

        stats_label = ttk.Label(stats_frame, text=stats_text, font=('Arial', 10))
        stats_label.grid(row=0, column=0, sticky=tk.W)

        # Biens par type
        types_frame = ttk.LabelFrame(reports_frame, text="Répartition par type de bien", padding="15")
        types_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        types_data = self.db.execute_query("""
            SELECT type_bien, COUNT(*) as nombre,
                   SUM(CASE WHEN disponible = 0 THEN 1 ELSE 0 END) as loues
            FROM biens
            GROUP BY type_bien
        """)

        if types_data:
            types_text = "Type de bien | Total | Loués | Disponibles\n"
            types_text += "-" * 45 + "\n"
            for type_bien, total, loues in types_data:
                disponibles = total - loues
                types_text += f"{type_bien:<15} | {total:>3} | {loues:>3} | {disponibles:>3}\n"
        else:
            types_text = "Aucune donnée disponible"

        types_label = ttk.Label(types_frame, text=types_text, font=('Courier', 9))
        types_label.grid(row=0, column=0, sticky=tk.W)

        # Voitures par marque
        voitures_frame = ttk.LabelFrame(reports_frame, text="Répartition par marque de voiture", padding="15")
        voitures_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        voitures_data = self.db.execute_query("""
            SELECT marque, COUNT(*) as nombre,
                   SUM(CASE WHEN disponible = 0 THEN 1 ELSE 0 END) as louees
            FROM voitures
            GROUP BY marque
        """)

        if voitures_data:
            voitures_text = "Marque        | Total | Louées | Disponibles\n"
            voitures_text += "-" * 45 + "\n"
            for marque, total, louees in voitures_data:
                disponibles = total - louees
                voitures_text += f"{marque:<12} | {total:>3} | {louees:>4} | {disponibles:>3}\n"
        else:
            voitures_text = "Aucune donnée de voiture disponible"

        voitures_label = ttk.Label(voitures_frame, text=voitures_text, font=('Courier', 9))
        voitures_label.grid(row=0, column=0, sticky=tk.W)

        # Boutons d'export (fonctionnalité future)
        export_frame = ttk.LabelFrame(reports_frame, text="Export", padding="15")
        export_frame.grid(row=3, column=0, sticky=(tk.W, tk.E))

        ttk.Button(export_frame, text="Exporter les clients (CSV)",
                  command=lambda: messagebox.showinfo("Info", "Fonctionnalité à venir")).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(export_frame, text="Exporter les biens (CSV)",
                  command=lambda: messagebox.showinfo("Info", "Fonctionnalité à venir")).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(export_frame, text="Exporter les voitures (CSV)",
                  command=lambda: messagebox.showinfo("Info", "Fonctionnalité à venir")).grid(row=0, column=2, padx=(0, 10))
        ttk.Button(export_frame, text="Exporter les contrats (CSV)",
                  command=lambda: messagebox.showinfo("Info", "Fonctionnalité à venir")).grid(row=0, column=3)

if __name__ == "__main__":
    root = tk.Tk()
    app = LocationApp(root)
    root.mainloop()
