#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour ajouter des données de test au système de gestion de location
"""

import sqlite3
from datetime import date, timed<PERSON><PERSON>

def add_test_data():
    """Ajoute des données de test à la base de données"""
    conn = sqlite3.connect('location_system.db')
    cursor = conn.cursor()
    
    print("Ajout des données de test...")
    
    # Clients de test
    clients_test = [
        ("<PERSON>", "<PERSON><PERSON>", "<EMAIL>", "0123456789", "123 Rue de la Paix, Paris"),
        ("<PERSON>", "<PERSON>", "<EMAIL>", "0234567890", "456 Avenue des Champs, Lyon"),
        ("<PERSON>", "<PERSON>", "<EMAIL>", "0345678901", "789 Boulevard Victor Hugo, Marseille"),
        ("<PERSON>", "<PERSON><PERSON>", "<EMAIL>", "0456789012", "321 Rue de la République, Toulouse"),
        ("<PERSON>", "<PERSON><PERSON>", "<EMAIL>", "0567890123", "654 Avenue de la Liberté, Nice")
    ]
    
    for client in clients_test:
        cursor.execute("""
            INSERT INTO clients (prenom, nom, email, telephone, adresse) 
            VALUES (?, ?, ?, ?, ?)
        """, client)
    
    print(f"✓ {len(clients_test)} clients ajoutés")
    
    # Biens de test
    biens_test = [
        ("Appartement", "12 Rue de Rivoli, Paris", 75.0, 3, 1200.0, True, "Appartement moderne avec vue sur Seine"),
        ("Maison", "34 Avenue des Roses, Lyon", 120.0, 4, 1500.0, True, "Maison avec jardin"),
        ("Studio", "56 Rue du Commerce, Marseille", 25.0, 1, 600.0, True, "Studio meublé centre-ville"),
        ("Appartement", "78 Boulevard Haussmann, Paris", 90.0, 3, 1800.0, True, "Appartement haussmannien"),
        ("Maison", "90 Chemin des Vignes, Bordeaux", 150.0, 5, 2000.0, True, "Maison de caractère"),
        ("Studio", "23 Rue Nationale, Lille", 30.0, 1, 500.0, True, "Studio étudiant"),
        ("Appartement", "45 Cours Lafayette, Lyon", 85.0, 3, 1050.0, False, "Appartement rénové"),
        ("Studio", "67 Rue de la Pompe, Paris", 35.0, 1, 750.0, False, "Studio lumineux")
    ]

    for bien in biens_test:
        cursor.execute("""
            INSERT INTO biens (type_bien, adresse, superficie, nb_pieces, loyer_mensuel, disponible, description)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, bien)
    
    print(f"✓ {len(biens_test)} biens ajoutés")
    
    # Voitures de test
    voitures_test = [
        ("Peugeot", "308", 2020, "AB-123-CD", "Blanche", "Essence", "Manuelle", 45000, 35.0, True, "Berline confortable"),
        ("Renault", "Clio", 2019, "EF-456-GH", "Rouge", "Essence", "Manuelle", 38000, 30.0, True, "Citadine économique"),
        ("BMW", "X3", 2021, "IJ-789-KL", "Noire", "Diesel", "Automatique", 25000, 65.0, True, "SUV premium"),
        ("Tesla", "Model 3", 2022, "MN-012-OP", "Bleu", "Électrique", "Automatique", 15000, 80.0, True, "Berline électrique"),
        ("Volkswagen", "Golf", 2018, "QR-345-ST", "Grise", "Diesel", "Manuelle", 55000, 40.0, False, "Compacte polyvalente"),
        ("Mercedes", "Classe A", 2021, "UV-678-WX", "Argent", "Essence", "Automatique", 20000, 70.0, False, "Berline de luxe")
    ]
    
    for voiture in voitures_test:
        cursor.execute("""
            INSERT INTO voitures (marque, modele, annee, immatriculation, couleur, carburant, transmission, kilometrage, prix_jour, disponible, description) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, voiture)
    
    print(f"✓ {len(voitures_test)} voitures ajoutées")
    
    # Contrats de test (pour les biens et voitures non disponibles)
    contrats_test = [
        # Contrats de biens
        (1, 7, None, 'bien', (date.today() - timedelta(days=30)).isoformat(), None, 1050.0, 2100.0, "actif"),  # Dupont - Appartement Lyon
        (2, 8, None, 'bien', (date.today() - timedelta(days=15)).isoformat(), None, 750.0, 1500.0, "actif"),   # Martin - Studio Paris
        # Contrats de voitures
        (3, None, 5, 'voiture', (date.today() - timedelta(days=10)).isoformat(), (date.today() + timedelta(days=20)).isoformat(), 40.0, 200.0, "actif"),  # Bernard - Golf
        (4, None, 6, 'voiture', (date.today() - timedelta(days=5)).isoformat(), (date.today() + timedelta(days=25)).isoformat(), 70.0, 350.0, "actif")   # Durand - Mercedes
    ]
    
    for contrat in contrats_test:
        cursor.execute("""
            INSERT INTO contrats (client_id, bien_id, voiture_id, type_location, date_debut, date_fin, prix_unitaire, depot_garantie, statut) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, contrat)
    
    print(f"✓ {len(contrats_test)} contrats ajoutés")
    
    conn.commit()
    conn.close()
    
    print("\n✅ Toutes les données de test ont été ajoutées avec succès!")
    print("Vous pouvez maintenant tester l'application avec ces données.")

if __name__ == "__main__":
    add_test_data()
