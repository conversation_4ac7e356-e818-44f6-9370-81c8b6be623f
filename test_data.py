#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour ajouter des données de test au système de gestion de location
"""

import sqlite3
from datetime import date, timed<PERSON><PERSON>

def add_test_data():
    """Ajoute des données de test à la base de données"""
    
    # Connexion à la base de données
    conn = sqlite3.connect("location_system.db")
    cursor = conn.cursor()
    
    print("Ajout de données de test...")
    
    # Clients de test
    clients_test = [
        ("<PERSON><PERSON>", "<PERSON>", "0123456789", "<EMAIL>", "123 Rue de la Paix, 75001 Paris"),
        ("<PERSON>", "<PERSON>", "0234567890", "<EMAIL>", "456 Avenue des Champs, 69001 Lyon"),
        ("<PERSON>", "<PERSON>", "0345678901", "<EMAIL>", "789 Boulevard Victor Hugo, 13001 Marseille"),
        ("<PERSON><PERSON>", "<PERSON>", "0456789012", "<EMAIL>", "321 Rue de la République, 31000 Toulouse"),
        ("<PERSON><PERSON>", "<PERSON>", "0567890123", "<EMAIL>", "654 Avenue de la Liberté, 44000 Nantes")
    ]
    
    for client in clients_test:
        cursor.execute("""
            INSERT INTO clients (nom, prenom, telephone, email, adresse) 
            VALUES (?, ?, ?, ?, ?)
        """, client)
    
    print(f"✓ {len(clients_test)} clients ajoutés")
    
    # Biens de test
    biens_test = [
        ("Appartement", "15 Rue de Rivoli, 75001 Paris", 45.0, 2, 1200.0, 150.0, True, "Appartement 2 pièces au cœur de Paris"),
        ("Studio", "28 Avenue Montaigne, 75008 Paris", 25.0, 1, 800.0, 100.0, True, "Studio moderne avec vue sur avenue"),
        ("Maison", "42 Rue des Roses, 69002 Lyon", 120.0, 5, 1800.0, 200.0, True, "Maison familiale avec jardin"),
        ("Appartement", "33 Cours Mirabeau, 13001 Marseille", 65.0, 3, 950.0, 120.0, True, "Appartement 3 pièces centre-ville"),
        ("Loft", "18 Rue Saint-Pierre, 31000 Toulouse", 80.0, 2, 1100.0, 80.0, True, "Loft industriel rénové"),
        ("Bureau", "55 Avenue de la Gare, 44000 Nantes", 40.0, 2, 600.0, 50.0, True, "Bureau professionnel équipé"),
        ("Appartement", "12 Place Bellecour, 69002 Lyon", 55.0, 2, 1050.0, 130.0, False, "Appartement de standing"),
        ("Studio", "7 Rue du Commerce, 75015 Paris", 22.0, 1, 750.0, 90.0, False, "Studio étudiant meublé")
    ]
    
    for bien in biens_test:
        cursor.execute("""
            INSERT INTO biens (type_bien, adresse, superficie, nb_pieces, loyer_mensuel, charges, disponible, description) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, bien)
    
    print(f"✓ {len(biens_test)} biens ajoutés")
    
    # Contrats de test (pour les biens non disponibles)
    contrats_test = [
        (1, 7, (date.today() - timedelta(days=30)).isoformat(), None, 1050.0, 2100.0, "actif"),  # Dupont - Appartement Lyon
        (2, 8, (date.today() - timedelta(days=15)).isoformat(), None, 750.0, 1500.0, "actif")   # Martin - Studio Paris
    ]
    
    for contrat in contrats_test:
        cursor.execute("""
            INSERT INTO contrats (client_id, bien_id, date_debut, date_fin, loyer_mensuel, depot_garantie, statut) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, contrat)
    
    print(f"✓ {len(contrats_test)} contrats ajoutés")
    
    # Valider les changements
    conn.commit()
    conn.close()
    
    print("\n🎉 Données de test ajoutées avec succès !")
    print("\nVous pouvez maintenant :")
    print("1. Lancer l'application : python main.py")
    print("2. Explorer les différentes sections")
    print("3. Tester les fonctionnalités d'ajout/modification/suppression")

if __name__ == "__main__":
    try:
        add_test_data()
    except Exception as e:
        print(f"❌ Erreur lors de l'ajout des données de test : {e}")
        print("Assurez-vous que l'application a été lancée au moins une fois pour créer la base de données.")
