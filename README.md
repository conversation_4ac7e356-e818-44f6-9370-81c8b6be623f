# Système de Gestion de Location

Une application de bureau développée en Python avec Tkinter pour gérer les locations immobilières.

## Fonctionnalités

### 🏠 Tableau de bord
- Vue d'ensemble des statistiques
- Actions rapides
- Indicateurs clés de performance

### 👥 Gestion des Clients
- Ajouter, modifier, supprimer des clients
- Informations complètes : nom, prénom, téléphone, email, adresse
- Liste triée et recherchable

### 🏢 Gestion des Biens
- Gestion complète du patrimoine immobilier
- Types de biens : appartement, maison, studio, loft, bureau, local commercial
- Informations détaillées : adresse, superficie, nombre de pièces, loyer, charges
- Statut de disponibilité

### 📋 Gestion des Contrats
- Création et modification de contrats de location
- Association client-bien
- Gestion des dates de début et fin
- Calcul automatique des loyers et dépôts de garantie
- Résiliation de contrats

### 📊 Rapports et Statistiques
- Statistiques générales du parc immobilier
- Taux d'occupation
- Revenus mensuels totaux
- Répartition par type de bien
- Export de données (fonctionnalité future)

## Installation et Utilisation

### Prérequis
- Python 3.6 ou supérieur
- Tkinter (généralement inclus avec Python)

### Installation
1. Clonez ou téléchargez les fichiers
2. Assurez-vous que Python et Tkinter sont installés
3. Lancez l'application :

```bash
python main.py
```

### Première utilisation
1. L'application créera automatiquement une base de données SQLite (`location_system.db`)
2. Commencez par ajouter des clients via le menu "Gestion Clients"
3. Ajoutez des biens immobiliers via "Gestion Biens"
4. Créez des contrats de location via "Gestion Contrats"

## Structure de la Base de Données

### Table `clients`
- id (clé primaire)
- nom, prenom
- telephone, email, adresse
- date_creation

### Table `biens`
- id (clé primaire)
- type_bien, adresse
- superficie, nb_pieces
- loyer_mensuel, charges
- disponible (booléen)
- description, date_creation

### Table `contrats`
- id (clé primaire)
- client_id, bien_id (clés étrangères)
- date_debut, date_fin
- loyer_mensuel, depot_garantie
- statut, date_creation

## Fonctionnalités Techniques

- Interface graphique moderne avec Tkinter
- Base de données SQLite intégrée
- Validation des données
- Gestion des erreurs
- Fenêtres modales pour les formulaires
- Mise à jour automatique des listes

## Améliorations Futures

- Export CSV/PDF des données
- Système de sauvegarde automatique
- Gestion des paiements de loyers
- Calendrier des échéances
- Impression de contrats
- Recherche avancée et filtres
- Graphiques et tableaux de bord avancés

## Support

Pour toute question ou suggestion d'amélioration, n'hésitez pas à nous contacter.
